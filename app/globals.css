@import "tailwindcss";

@keyframes slide {
  0% { transform: translate(0, 0); }
  100% { transform: translate(70px, 70px); }
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes sunPulse {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 0.7;
  }
}

@keyframes orbShrink {
  0% {
    transform: translateX(-50%) translateY(45%) scale(1.5);
    opacity: 0.2;
  }
  100% {
    transform: translateX(-50%) translateY(45%) scale(1);
    opacity: 1;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes screenshot-pulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(0.98);
  }
  50% {
    opacity: 0.4;
    transform: scale(1);
  }
}

@keyframes camera-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(-5deg);
  }
  75% {
    transform: translateY(5px) rotate(5deg);
  }
}

@keyframes lens-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pushUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInSmooth {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Theme configuration for Tailwind CSS v4 */
@theme {
  --color-background: hsl(0 0% 100%);
  --color-foreground: hsl(240 10% 3.9%);
  --color-card: hsl(0 0% 100%);
  --color-card-foreground: hsl(240 10% 3.9%);
  --color-popover: hsl(0 0% 100%);
  --color-popover-foreground: hsl(240 10% 3.9%);
  --color-primary: hsl(25 95% 53%);
  --color-primary-foreground: hsl(0 0% 98%);
  --color-secondary: hsl(240 4.8% 95.9%);
  --color-secondary-foreground: hsl(240 5.9% 10%);
  --color-muted: hsl(240 4.8% 95.9%);
  --color-muted-foreground: hsl(240 3.8% 46.1%);
  --color-accent: hsl(240 4.8% 95.9%);
  --color-accent-foreground: hsl(240 5.9% 10%);
  --color-destructive: hsl(0 84.2% 60.2%);
  --color-destructive-foreground: hsl(0 0% 98%);
  --color-border: hsl(240 5.9% 90%);
  --color-input: hsl(240 5.9% 90%);
  --color-ring: hsl(25 95% 53%);
  
  --radius: 0.5rem;
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  /* Radial gradient utilities */
  .bg-gradient-radial {
    background-image: radial-gradient(circle, var(--tw-gradient-stops));
  }
  
  /* Conic gradient utilities */
  .bg-gradient-conic {
    background-image: conic-gradient(var(--tw-gradient-stops));
  }
}

@layer base {
  * {
    border-color: theme('colors.border');
  }
  body {
    background-color: theme('colors.background');
    color: theme('colors.foreground');
  }
}

@layer utilities {
  .animate-gradient-shift {
    background-size: 400% 400%;
    animation: gradient-shift 8s ease infinite;
  }
  
  .animate-camera-float {
    animation: camera-float 3s ease-in-out infinite;
  }
  
  .animate-lens-rotate {
    animation: lens-rotate 2s linear infinite;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animate-push-up {
    animation: pushUp 0.4s ease-out forwards;
  }
  
  .animate-fade-in-smooth {
    opacity: 0;
    animation: fadeInSmooth 0.6s ease-out forwards;
  }
  
  .animate-fade-in-up {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out forwards;
  }
}
